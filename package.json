{"name": "spog-container-ui", "version": "0.0.11", "private": true, "dependencies": {"@craco/craco": "^6.4.5", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^5.17.1", "@mui/styles": "^5.17.1", "@nv2/nv2-pkg-js-shared-components": "^2.46.0", "@nv2/nv2-pkg-js-theme": "^2.8.3", "ajv": "^8.17.1", "axios": "^1.8.4", "core-js": "^2.6.12", "craco-sass-resources-loader": "^1.1.0", "external-remotes-plugin": "^1.0.0", "globals": "^15.14.0", "jest-junit": "^16.0.0", "material-table": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.3.1", "react-cookie": "^4.1.1", "react-dom": "^18.3.1", "react-icons": "^4.4.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-show-more-text": "^1.5.2", "sass-loader": "^13.0.2", "ts-jest": "^29.3.1", "typescript": "^5.8.3"}, "scripts": {"start": "GENERATE_SOURCEMAP=false PORT=3007 craco start", "start:on-windows": "set GENERATE_SOURCEMAP=false && set PORT=3007 && craco start", "build": "craco build", "lint": "eslint src", "lint:fix": "eslint --fix src", "stylelint": "stylelint \"**/*.scss\"", "test": "jest --maxWorkers=10% --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "react-scripts eject", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "axios-mock-adapter": "^2.1.0", "eslint": "^9.24.0", "eslint-config-airbnb": "^19.0.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.7.0", "stylelint": "^14.16.1", "stylelint-config-standard-scss": "^5.0.0", "stylelint-scss": "^4.7.0", "webpack": "^5.99.6"}, "overrides": {"form-data": "^4.0.4"}}