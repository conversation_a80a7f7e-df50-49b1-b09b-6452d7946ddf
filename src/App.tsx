import { ThemeProvider } from '@mui/material';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import setFavicon from '@nv2/nv2-pkg-js-theme/src/components/setFavicon';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import GTMWrapper, { useGTM } from '@nv2/nv2-pkg-js-shared-components/lib/GTMWrapper';
import AppContextProvider from 'AppContextProvider';
import { ConfigSettingsService, CookiesService, HTTPService } from 'core/services';
import { coreAxios } from 'core/services/HTTPService';
import React, {
  FC, useEffect, useState, useCallback,
} from 'react';
import { useCookies } from 'react-cookie';
import BaseLayout from 'layouts/BaseLayout';
import { Route, Routes, useNavigate } from 'react-router-dom';
import GTM_ID from 'core/utilities/constants';
import './assets/styles/reset.scss';

const App: FC = () => {
  // TODO temporary fix. When find solution how to combine mui from 2 remote components remove it.
  const [themeName, setThemeName] = useState('bt');
  const { pushToDataLayer } = useGTM();
  const [isInitialized, setIsInitialized] = useState(false);
  const [appConfig, setAppConfig] = useState<Record<string, unknown> | null>(null);
  const navigate = useNavigate();
  const [cookies] = useCookies();
  const currentTheme = themeConfig[themeName];

  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const getProdLogOutUrl = (keycloakLogOutUrl: string) => {
    const keycloakLogoutUrlFull = `${keycloakLogOutUrl}?redirect_uri=${window.location.origin}`;

    return `/oauth2/sign_out?rd=${encodeURIComponent(keycloakLogoutUrlFull)}`;
  };

  const logOut = async (url: string) => {
    window.location.replace(url);
  };

  const getLogoutUrl = useCallback((url: string) => (isDevelopmentMode
    ? process.env.REACT_APP_LOGOUT_URL
    : getProdLogOutUrl(url)), [isDevelopmentMode]);

  const redirectToLogin = useCallback(() => {
    const currentPathname = window.location.href;
    const entryPointUrl = `${process.env.REACT_APP_LOGIN_URL}?entryPath=${currentPathname}`;

    navigate(entryPointUrl);
  }, [navigate]);

  const authenticateForDevelopmentMode = useCallback(() => {
    const cookieDoesntHaveAccessToken = !CookiesService.getAccessToken(cookies).value;

    if (cookieDoesntHaveAccessToken) {
      redirectToLogin();
    }

    HTTPService.setAccessToken(coreAxios, cookies);
  }, [cookies, redirectToLogin]);

  const setupInitialData = useCallback(async () => {
    const { data } = await ConfigSettingsService.getAppVariables();

    const logoutUrl = getLogoutUrl(data?.themeName === 'bt' ? data?.coreUiUrl : data.keycloakLogoutUrl);

    if (isDevelopmentMode) {
      authenticateForDevelopmentMode();
    }

    HTTPService.setDefaultGlobalConfig(coreAxios, data.apiUrl);

    HTTPService.setCorsError(coreAxios, logOut, logoutUrl);

    setFavicon(data.themeName);
    setThemeName(data.themeName);
    setAppConfig(data);
    pushToDataLayer({ env: data.env });

    setIsInitialized(true);
  }, [getLogoutUrl, isDevelopmentMode, authenticateForDevelopmentMode]);

  const handleGTMError = (error: Error, context: string) => {
    // eslint-disable-next-line no-console
    console.error(`GTM Error: ${context}`, error);
  };

  useEffect(() => {
    setupInitialData();
  }, [setupInitialData]);

  return (
    <ThemeProvider
      theme={theme(currentTheme)}
    >
      <AppContextProvider isInitialized={isInitialized} themeValue={{ themeName, currentTheme }} appConfig={appConfig}>
        <GTMWrapper
          gtmId={GTM_ID}
          onError={handleGTMError}
        />
        <Routes>
          <Route path="*" element={<BaseLayout />} />
        </Routes>
      </AppContextProvider>
    </ThemeProvider>
  );
};

export default App;
