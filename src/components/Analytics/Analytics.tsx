import React, { FC } from 'react';
import { useAppContext } from 'AppContextProvider';
import IframeViewer from '@nv2/nv2-pkg-js-shared-components/lib/IframeViewer';

import './Analytics.scss';

const Analytics: FC = () => {
  const { appConfig } = useAppContext();
  const analyticsUrl = (appConfig?.analyticsUrl as string) || 'https://analytics.dev.spogconnected.com';

  return (
    <div className="analyticsContainer" data-testid="analytics-container">
      <IframeViewer
        src={analyticsUrl}
        className="analyticsIframe"
        title="Superset Analytics Panel"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
      />
    </div>
  );
};

export default Analytics;
