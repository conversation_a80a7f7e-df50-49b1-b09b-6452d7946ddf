import React, {
  createContext, ReactNode, useEffect, useState, useContext, useMemo, useCallback,
} from 'react';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { useGTM } from '@nv2/nv2-pkg-js-shared-components/lib/GTMWrapper';
import { IUser } from './user.model';
import { getUser } from './api.service';

interface IAppContext {
  user: IUser | undefined,
  isLoading: boolean,
  primaryColor: string,
  secondaryColor: string,
  themeName: string,
  getBrandColors: (color: string) => ({ [index: number]: string }),
  appConfig: Record<string, unknown> | null,
}

interface IAppContextProvider {
  children: ReactNode | ReactNode[],
  isInitialized: boolean,
  themeValue: {
    themeName: string,
    currentTheme: Record<string, string>
  },
  appConfig: Record<string, unknown> | null,
}

export const AppContext = createContext<Partial<IAppContext>>({});

export const useAppContext = () => useContext(AppContext);

const AppContextProvider = ({
  children, isInitialized, themeValue, appConfig,
}: IAppContextProvider) => {
  const { themeName, currentTheme } = themeValue;
  const [isLoading, setIsLoading] = useState(true);
  const { pushToDataLayer } = useGTM();
  const [user, setUser] = useState<IUser | undefined>();

  const getCurrentThemeColors = useCallback((color: string) => getBrandColors(color, themeName), [themeName]);

  const themeVariables = useMemo(() => ({
    primaryColor: currentTheme?.primaryColor,
    secondaryColor: currentTheme?.secondaryColor,
    getBrandColors: getCurrentThemeColors,
    themeName,
  }), [currentTheme?.primaryColor, currentTheme?.secondaryColor, getCurrentThemeColors, themeName]);

  useEffect(() => {
    if (!isInitialized) return;

    (async () => {
      try {
        setIsLoading(true);
        const { data } = await getUser();
        setUser(data);
        pushToDataLayer({ ...data });
        localStorage.setItem('userDetails', JSON.stringify(data));
      } finally {
        setIsLoading(false);
      }
    })();
  }, [isInitialized]);

  const value = useMemo(() => ({
    user,
    isLoading,
    appConfig,
    ...themeValue,
    ...themeVariables,
  }), [user, appConfig, isLoading, themeValue, themeVariables]);

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContextProvider;
